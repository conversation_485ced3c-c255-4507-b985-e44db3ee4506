<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Data Format Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <h1>Chart Data Format Test</h1>
    
    <h2>Filter = false (Simple Array)</h2>
    <canvas id="chart1" width="400" height="200"></canvas>
    
    <h2>Filter = true (Array of Arrays)</h2>
    <canvas id="chart2" width="400" height="200"></canvas>

    <script>
        // Test data format when filter=false
        const dataFilterFalse = {
            filter: 'false',
            labels: ['pH value (regulation): 13', 'Spirit (regulation): 14', 'Water deficiency (regulation): 14', 'Oxygen deficiency (regulation): 20', 'Reaction (regulation): 30'],
            results: [13.0, 14.0, 14.0, 20.0, 30.0]
        };

        // Test data format when filter=true
        const dataFilterTrue = {
            filter: 'true',
            labels: ['Oxygen deficiency (regulation)', 'pH value (regulation)', 'Reaction (regulation)', 'Spirit (regulation)', 'Water deficiency (regulation)'],
            results: [
                [4, 4, 4, 4, 3],
                [3, 3, 3, 3, 4]
            ]
        };

        // Updated getFormattedDatasets function from our fix
        function getFormattedDatasets(datasets, filters) {
            // Convert string filter to boolean - when filter=false, results is simple array
            // when filter=true, results is array of arrays for multi-dataset charts
            if (filters === 'false' || filters === false || !filters) {
                const chartColors = getChartColors(datasets);
                return [{
                    data: datasets,
                    backgroundColor: chartColors.backgroundColor,
                    borderColor: chartColors.borderColor,
                    borderWidth: 1
                }];
            }
            
            // If filters=true, datasets is array of arrays for multiple datasets
            if (filters === 'true' || filters === true) {
                return datasets.map((data, index) => ({
                    data: data,
                    backgroundColor: index === 0 ? 'rgba(255, 99, 132, 0.2)' : 'rgba(75, 192, 192, 0.2)',
                    borderColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
                    borderWidth: 1
                }));
            }
        }

        function getChartColors(dataSet) {
            if (!dataSet || !Array.isArray(dataSet)) {
                return { backgroundColor: [], borderColor: [] };
            }

            const getColor = value => {
                if (value <= 10) return ['rgba(232, 78, 27, 1)', 'rgb(232, 78, 27)'];
                if (value <= 69) return ['rgba(248, 177, 51, 1)', 'rgb(248, 177, 51)'];
                if (value <= 100) return ['rgba(47, 171, 102, 1)', 'rgb(47, 171, 102)'];
                return ['rgba(0, 0, 0, 0.2)', 'rgb(0, 0, 0)'];
            };

            return dataSet.reduce((colors, value) => {
                const [bgColor, borderColor] = getColor(value);
                colors.backgroundColor.push(bgColor);
                colors.borderColor.push(borderColor);
                return colors;
            }, { backgroundColor: [], borderColor: [] });
        }

        // Test Chart 1 - filter=false
        const ctx1 = document.getElementById('chart1').getContext('2d');
        const chart1 = new Chart(ctx1, {
            type: 'bar',
            data: {
                labels: dataFilterFalse.labels,
                datasets: getFormattedDatasets(dataFilterFalse.results, dataFilterFalse.filter)
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Filter = false (Single Dataset)'
                    }
                }
            }
        });

        // Test Chart 2 - filter=true
        const ctx2 = document.getElementById('chart2').getContext('2d');
        const chart2 = new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: dataFilterTrue.labels,
                datasets: getFormattedDatasets(dataFilterTrue.results, dataFilterTrue.filter)
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Filter = true (Multiple Datasets)'
                    }
                }
            }
        });

        // Log the results to console for debugging
        console.log('Filter false datasets:', getFormattedDatasets(dataFilterFalse.results, dataFilterFalse.filter));
        console.log('Filter true datasets:', getFormattedDatasets(dataFilterTrue.results, dataFilterTrue.filter));
    </script>
</body>
</html>
