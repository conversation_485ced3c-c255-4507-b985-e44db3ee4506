<?php

namespace Tests\Feature\Dashboard;

use Tests\TestCase;
use Livewire\Livewire;
use App\Livewire\Dashboard\Widgets\Graphs\Bar;
use App\Livewire\Dashboard\Widgets\Graphs\Line;
use App\Livewire\Dashboard\Widgets\Graphs\Radar;
use App\Livewire\Dashboard\Widgets\Graphs\PolarArea;

class ChartWidgetDataFormatTest extends TestCase
{
    /** @test */
    public function bar_chart_handles_filter_false_data_format()
    {
        // Mock data format when filter=false (simple array)
        $mockResults = [
            'success' => true,
            'filter' => false,
            'labels' => ['pH value', 'Spirit', 'Water deficiency', 'Oxygen deficiency', 'Reaction'],
            'results' => [13.0, 14.0, 14.0, 20.0, 30.0],
            'diagram_type' => 'bar',
        ];

        $component = Livewire::test(Bar::class, [
            'poolId' => 1,
            'settings' => []
        ]);

        // Mock the results property
        $component->set('results', $mockResults);

        $component->assertViewIs('livewire.dashboard.widgets.graphs.bar');
        $component->assertSee('widget_chart');
    }

    /** @test */
    public function bar_chart_handles_filter_true_data_format()
    {
        // Mock data format when filter=true (array of arrays)
        $mockResults = [
            'success' => true,
            'filter' => true,
            'labels' => ['Oxygen deficiency', 'pH value', 'Reaction', 'Spirit', 'Water deficiency'],
            'results' => [
                [4, 4, 4, 4, 3], // First dataset
                [3, 3, 3, 3, 4], // Second dataset
            ],
            'diagram_type' => 'bar',
        ];

        $component = Livewire::test(Bar::class, [
            'poolId' => 1,
            'settings' => []
        ]);

        // Mock the results property
        $component->set('results', $mockResults);

        $component->assertViewIs('livewire.dashboard.widgets.graphs.bar');
        $component->assertSee('widget_chart');
    }

    /** @test */
    public function line_chart_handles_both_data_formats()
    {
        // Test filter=false format
        $mockResultsFalse = [
            'success' => true,
            'filter' => false,
            'labels' => ['pH value', 'Spirit', 'Water deficiency'],
            'results' => [13.0, 14.0, 14.0],
            'diagram_type' => 'line',
        ];

        $component = Livewire::test(Line::class, [
            'poolId' => 1,
            'settings' => []
        ]);

        $component->set('results', $mockResultsFalse);
        $component->assertViewIs('livewire.dashboard.widgets.graphs.line');

        // Test filter=true format
        $mockResultsTrue = [
            'success' => true,
            'filter' => true,
            'labels' => ['pH value', 'Spirit', 'Water deficiency'],
            'results' => [
                [13.0, 14.0, 14.0],
                [15.0, 16.0, 17.0],
            ],
            'diagram_type' => 'line',
        ];

        $component->set('results', $mockResultsTrue);
        $component->assertViewIs('livewire.dashboard.widgets.graphs.line');
    }

    /** @test */
    public function radar_chart_handles_both_data_formats()
    {
        $component = Livewire::test(Radar::class, [
            'poolId' => 1,
            'settings' => []
        ]);

        // Test filter=false format
        $mockResultsFalse = [
            'success' => true,
            'filter' => false,
            'labels' => ['pH value', 'Spirit', 'Water deficiency'],
            'results' => [13.0, 14.0, 14.0],
            'diagram_type' => 'radar',
        ];

        $component->set('results', $mockResultsFalse);
        $component->assertViewIs('livewire.dashboard.widgets.graphs.radar');
    }

    /** @test */
    public function polar_area_chart_handles_both_data_formats()
    {
        $component = Livewire::test(PolarArea::class, [
            'poolId' => 1,
            'settings' => []
        ]);

        // Test filter=false format
        $mockResultsFalse = [
            'success' => true,
            'filter' => false,
            'labels' => ['pH value', 'Spirit', 'Water deficiency'],
            'results' => [13.0, 14.0, 14.0],
            'diagram_type' => 'polarArea',
        ];

        $component->set('results', $mockResultsFalse);
        $component->assertViewIs('livewire.dashboard.widgets.graphs.polar-area');
    }
}
