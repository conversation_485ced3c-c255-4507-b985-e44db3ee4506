<div>
    <canvas id="{{$poolId}}_widget_chart"></canvas>
</div>

@assets
<script src="{{ asset('/js/chart/chart.min.js') }}"></script>
<script src="{{ asset('/js/chart/chartjs-plugin-annotation.min.js') }}"></script>
@endassets

@script
<script>
    let id = '{{$poolId}}';
    let filter = '{{$results['filter']}}';
    let results = @json($results['results']);
    let labels = @json($results['labels']);
    let chartType = 'radar';

    function createRadarChart(id, labels, datasets, filters) {
        const canvasId = `${id}_widget_chart`;
        const ctx = document.getElementById(canvasId).getContext('2d');

        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false, position: 'top' },
                zoom: {
                    pan: { enabled: true, mode: 'xy' },
                    zoom: { enabled: true, mode: 'xy' },
                },
            },
            scales: {
                r: {
                    beginAtZero: true,
                    ticks: { stepSize: 10, reverse: false },
                    pointLabels: { display: true, centerPointLabels: true, font: { size: 16 } }
                }
            }
        };

        const data = {
            labels: labels,
            datasets: getFormattedDatasets(datasets, filters),
        };

        if (window.chartInstances && window.chartInstances[canvasId]) {
            window.chartInstances[canvasId].destroy();
        }

        window.chartInstances = {
            ...(window.chartInstances || {}),
            [canvasId]: new Chart(ctx, { type: 'radar', data, options })
        };
    }

    function getFormattedDatasets(datasets, filters) {
        // Convert string filter to boolean - when filter=false, results is simple array
        // when filter=true, results is array of arrays for multi-dataset charts
        if (filters === 'false' || filters === false || !filters) {
            const chartColors = getChartColors(datasets);
            return [{
                data: datasets,
                fill: true,
                backgroundColor: 'transparent',
                pointBackgroundColor: chartColors.backgroundColor,
                pointBorderColor: chartColors.borderColor,
                pointHoverBackgroundColor: chartColors.borderColor,
                pointHoverBorderColor: chartColors.backgroundColor,
                pointRadius: 5,
            }];
        }

        // If filters=true, datasets is array of arrays for multiple datasets
        if (filters === 'true' || filters === true) {
            return datasets.map((data, index) => ({
                data: data,
                backgroundColor: index === 0 ? 'rgba(255, 99, 132, 0.2)' : 'rgba(75, 192, 192, 0.2)',
                borderColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
                borderWidth: 1,
                fill: true,
                pointBackgroundColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
                pointBorderColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
                pointRadius: 5,
            }));
        }
    }

    function getChartColors(dataSet) {
        // Add null safety check
        if (!dataSet || !Array.isArray(dataSet)) {
            return { backgroundColor: [], borderColor: [] };
        }

        return dataSet.reduce((colors, value) => {
            let bgColor, borderColor;
            if (value <= 10) {
                [bgColor, borderColor] = ['rgba(232, 78, 27, 1)', 'rgb(232, 78, 27)'];
            } else if (value <= 69) {
                [bgColor, borderColor] = ['rgba(248, 177, 51, 1)', 'rgb(248, 177, 51)'];
            } else if (value <= 100) {
                [bgColor, borderColor] = ['rgba(47, 171, 102, 1)', 'rgb(47, 171, 102)'];
            } else {
                [bgColor, borderColor] = ['rgba(0, 0, 0, 0.2)', 'rgb(0, 0, 0)'];
            }

            colors.backgroundColor.push(bgColor);
            colors.borderColor.push(borderColor);
            return colors;
        }, { backgroundColor: [], borderColor: [] });
    }

    // Initial chart creation
    createRadarChart(id, labels, results, filter);
</script>
@endscript